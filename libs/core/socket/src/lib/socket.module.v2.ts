import { CommonModule } from '@angular/common';
import { NgModule, inject } from '@angular/core';
import { Store } from '@ngrx/store';
import { TokenManagementService } from '@fincloud/core/auth';
import { MonitoringService } from '@fincloud/core/services';
import { SocketType } from './enums/socket-type';
import { CADR_SOCKET_SERVICE } from './utils/cadr-socket-service-injection-token';
import { CHAT_SOCKET_SERVICE } from './utils/chat-socket-service-injection-token';
import { PLATFORM_NOTIFICATION_SOCKET_SERVICE } from './utils/platform-notification-socket-service-injection-token';
import { PORTAL_SOCKET_SERVICE } from './utils/portal-socket-service-injection-token';
import { SocketOrchestrator } from './services/socket-orchestrator.service';

/**
 * Version 2 module using the new SocketOrchestrator. Keeps the same injection tokens.
 * Consumers can switch from NsCoreSocketModule to NsCoreSocketModuleV2 to adopt the split architecture.
 */
@NgModule({
  imports: [CommonModule],
  providers: [
    {
      provide: CHAT_SOCKET_SERVICE,
      useFactory: () =>
        new SocketOrchestrator(
          SocketType.CHAT,
          inject(TokenManagementService),
          inject(MonitoringService),
          inject(Store),
        ),
    },
    {
      provide: CADR_SOCKET_SERVICE,
      useFactory: () =>
        new SocketOrchestrator(
          SocketType.CADR,
          inject(TokenManagementService),
          inject(MonitoringService),
          inject(Store),
        ),
    },
    {
      provide: PLATFORM_NOTIFICATION_SOCKET_SERVICE,
      useFactory: () =>
        new SocketOrchestrator(
          SocketType.PLATFORM_NOTIFICATION,
          inject(TokenManagementService),
          inject(MonitoringService),
          inject(Store),
        ),
    },
    {
      provide: PORTAL_SOCKET_SERVICE,
      useFactory: () =>
        new SocketOrchestrator(
          SocketType.PORTAL,
          inject(TokenManagementService),
          inject(MonitoringService),
          inject(Store),
        ),
    },
  ],
})
export class NsCoreSocketModuleV2 {}

