import { Store } from '@ngrx/store';
import { concatLatestFrom } from '@ngrx/operators';
import {
  Observable,
  ReplaySubject,
  Subject,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { TokenManagementService } from '@fincloud/core/auth';
import { MonitoringService } from '@fincloud/core/services';
import { AccessTokenStatus } from '@fincloud/types/enums';
import { selectUserCustomerKey } from '@fincloud/core-state/user';
import { CoreStateLibAuthTokensPageActions } from '@fincloud/core-state/auth-tokens';
import { SocketType } from '../enums/socket-type';
import { SocketConfig } from '../models/socket-config';
import { SOCKET_CONFIG_MAP } from '../utils/socket-config-map';
import { SocketTransports } from '../enums/socket-transports';
import { SocketIoOptions } from '../models/socket-options';
import { SocketConnectionState } from '../models/socket-connection-state';
import { SocketConnectionStatus } from '../enums/socket-connection-status';
import { SocketQueryParams } from '../models/socket-query-params';
import { SocketEvents } from '../enums/socket-events';
import { MANUAL_DISCONNECT_REASON } from '../utils/manual-disconnect-reason';
import { UNEXPECTED_DISCONNECT_REASONS } from '../utils/unexpected-disconnect-reasons';
import { SocketClient } from './socket-client';
import { Socket } from 'socket.io-client';

/**
 * Version 2: Split architecture orchestrator built on top of a low-level SocketClient.
 * Implements the same public API as SocketNgxService to preserve compatibility.
 */
export class SocketOrchestrator {
  private readonly client: SocketClient;
  private readonly rooms = new Map<string, { joined: boolean }>();

  private initSocketObservable$: Observable<SocketConnectionState> | null = null;
  private destroy$$ = new Subject<void>();
  private readonly connectionState$ = new ReplaySubject<SocketConnectionState>(1);
  private readonly socket$ = new ReplaySubject<Socket>(1);

  constructor(
    private readonly socketType: SocketType,
    private readonly tokenManagementService: TokenManagementService,
    private readonly monitoringService: MonitoringService,
    private readonly store: Store,
    client?: SocketClient,
  ) {
    this.client = client ?? new SocketClient();
  }

  private get socketConfig(): SocketConfig {
    return SOCKET_CONFIG_MAP[this.socketType];
  }

  isConnected(): boolean {
    return this.client.isConnected();
  }

  initSocket(queryParams?: SocketQueryParams): Observable<SocketConnectionState> {
    if (this.initSocketObservable$) {
      return this.initSocketObservable$;
    }

    this.initSocketObservable$ = this.tokenManagementService.statusChange$.pipe(
      distinctUntilChanged(),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      tap(([status]) => {
        if (status === AccessTokenStatus.INVALID) {
          this.invalidTokenManualDisconnect();
        }
      }),
      filter(([status]) => status === AccessTokenStatus.VALID),
      map(([, customerKey]): SocketIoOptions => this.socketOptions(customerKey, queryParams)),
      switchMap(({ uri, options }) =>
        new Observable<SocketConnectionState>((observer) => {
          const socket = this.client.connect(uri, options);
          this.addSocketListeners(socket);
          this.addManagerSocketListeners(socket);

          // Re-join rooms upon fresh connect
          if (this.rooms.size > 0) {
            Array.from(this.rooms.keys()).forEach((room) => this.joinRoom(room).subscribe());
          }

          const sub = this.connectionState$.subscribe(observer);
          return () => sub.unsubscribe();
        }),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
      takeUntil(this.destroy$$),
    );

    return this.initSocketObservable$;
  }

  deactivateSocket(): Observable<void> {
    return new Observable<void>((observer) => {
      this.destroy$$.next();
      this.initSocketObservable$ = null;

      try {
        this.client.disconnect();
      } catch (e) {
        this.monitoringService.logWebsocketsError(e as Error);
      }

      this.rooms.clear();

      this.destroy$$.complete();
      this.destroy$$ = new Subject<void>();

      observer.next();
      observer.complete();
    });
  }

  joinRoom(roomParam: string): Observable<void> {
    if (this.rooms.get(roomParam)?.joined) {
      return new Observable<void>((o) => {
        o.next();
        o.complete();
      });
    }

    return new Observable<void>((observer) => {
      if (!this.client.instance) {
        observer.error(new Error(`Socket: ${this.socketConfig.context} is not connected. Room: ${roomParam}`));
        observer.complete();
        return;
      }

      this.client.joinRoom(roomParam).subscribe({
        next: () => {
          this.rooms.set(roomParam, { joined: true });
          observer.next();
          observer.complete();
        },
        error: (e) => observer.error(e),
      });
    });
  }

  leaveRoom(room: string): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.client.instance) {
        observer.error(new Error(`Socket: ${this.socketConfig.context} is not connected`));
        observer.complete();
        return;
      }

      if (!this.rooms.has(room)) {
        observer.next();
        observer.complete();
        return;
      }

      this.client.leaveRoom(room).subscribe({
        next: () => {
          this.rooms.delete(room);
          observer.next();
          observer.complete();
        },
        error: (e) => observer.error(e),
      });
    });
  }

  sendMessage<T>(sendDestination: string, message: T): Observable<void> {
    return new Observable<void>((observer) => {
      try {
        this.client.emit(sendDestination, message);
        observer.next();
      } catch (e) {
        this.monitoringService.logWebsocketsError(e as Error);
        observer.error(e);
      }
      observer.complete();
    });
  }

  getMessagesByDestination<T>(receiveDestination: string): Observable<T> {
    return this.socket$.pipe(
      switchMap((socket) =>
        new Observable<T>((observer) => {
          const handler = (m: T) => observer.next(m);
          socket.on(receiveDestination, handler);
          return () => socket.off(receiveDestination, handler);
        }),
      ),
    );
  }

  joinRoomAndListen<T>(roomParam: string, receiveDestination: string): Observable<T> {
    return new Observable<T>((observer) => {
      if (!this.client.instance) {
        observer.error(new Error(`Socket: ${this.socketConfig.context} is not connected. Room: ${roomParam}`));
        observer.complete();
        return;
      }

      const sub = this.joinRoom(roomParam).subscribe({
        next: () => {
          const sub2 = this.getMessagesByDestination<T>(receiveDestination).subscribe(observer);
          // chain teardown
          (observer as any)._innerSub = sub2; // best-effort
        },
        error: (e) => observer.error(e),
      });

      return () => {
        sub.unsubscribe();
        const inner: any = (observer as any)._innerSub;
        if (inner?.unsubscribe) inner.unsubscribe();
      };
    });
  }

  private addSocketListeners(socket: Socket): void {
    socket.on(SocketEvents.CONNECT, () => {
      this.socket$.next(socket);
      this.connectionState$.next({ status: SocketConnectionStatus.CONNECTED, socket });
    });

    socket.on(SocketEvents.CONNECT_ERROR, (error: Error) => {
      this.monitoringService.logWebsocketsError(error);
      this.connectionState$.next({ status: SocketConnectionStatus.CONNECT_ERROR, error });
    });

    socket.on(SocketEvents.DISCONNECT, (reason: string, details?: unknown) => {
      if (reason !== MANUAL_DISCONNECT_REASON && UNEXPECTED_DISCONNECT_REASONS.includes(reason)) {
        this.monitoringService.logWebsocketsError(
          new Error(`Unexpected disconnect from ${this.socketConfig.namespace}: ${reason}`),
        );
      }
      this.connectionState$.next({ status: SocketConnectionStatus.DISCONNECT, reason, details });
    });
  }

  private addManagerSocketListeners(socket: Socket): void {
    socket.io.on(SocketEvents.RECONNECT, () => {
      // Rejoin rooms and publish state
      Array.from(this.rooms.keys()).forEach((room) => this.joinRoom(room).subscribe());
      this.connectionState$.next({ status: SocketConnectionStatus.RECONNECT, socket });
    });

    socket.io.on(SocketEvents.RECONNECT_ERROR, (error: Error) => {
      this.monitoringService.logWebsocketsError(error);
      this.connectionState$.next({ status: SocketConnectionStatus.RECONNECT_ERROR, error });
    });

    socket.io.on(SocketEvents.RECONNECT_FAILED, () => {
      this.connectionState$.next({ status: SocketConnectionStatus.RECONNECT_FAILED });
    });
  }

  private invalidTokenManualDisconnect(): void {
    this.store.dispatch(CoreStateLibAuthTokensPageActions.obtainRefreshTokenBySocket());

    // Mark rooms as not joined; reconnect flow will rejoin
    Array.from(this.rooms.keys()).forEach((room) => this.rooms.set(room, { joined: false }));

    try {
      this.client.disconnect();
    } catch (e) {
      this.monitoringService.logWebsocketsError(e as Error);
    }

    this.connectionState$.next({ status: SocketConnectionStatus.DISCONNECT, reason: MANUAL_DISCONNECT_REASON });
  }

  private socketOptions(customerKey: string, queryParams?: SocketQueryParams): SocketIoOptions {
    const cfg = SOCKET_CONFIG_MAP[this.socketType];
    const uri = `${cfg.socketUrl}${cfg.namespace}`;
    const token = this.tokenManagementService.getToken(customerKey)?.tokenRaw?.accessToken ?? '';

    const options = {
      path: `/${cfg.context}`,
      query: { ...(queryParams || {}), token },
      transports: [/* prefer WS first */ 'websocket', 'polling'],
      autoConnect: true,
      reconnectionAttempts: 5,
    } as const;

    return { uri, options };
  }
}

