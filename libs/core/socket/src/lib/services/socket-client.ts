import { Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { SocketEvents } from '../enums/socket-events';

/**
 * Low-level Socket.IO client wrapper with no Angular dependencies.
 * Provides connect/disconnect, emit, room join/leave with ack, and event helpers.
 */
export class SocketClient {
  private socket: Socket | null = null;
  private readonly rooms = new Set<string>();

  connect(uri: string, options: any): Socket {
    if (this.socket && this.socket.connected) {
      return this.socket;
    }

    this.socket = io(uri, options);

    // On manager reconnect, rejoin rooms
    this.socket.io.on(SocketEvents.RECONNECT, () => {
      this.rooms.forEach((room) => this.joinRoomInternal(room));
    });

    return this.socket;
  }

  get instance(): Socket | null {
    return this.socket;
  }

  isConnected(): boolean {
    return !!this.socket && this.socket.connected;
  }

  disconnect(): void {
    try {
      this.socket?.removeAllListeners();
      this.socket?.disconnect();
    } finally {
      this.socket = null;
    }
  }

  emit<T>(event: string, payload: T): void {
    if (!this.socket) throw new Error('Socket not connected');
    this.socket.emit(event, payload);
  }

  fromEvent<T>(event: string): Observable<T> {
    return new Observable<T>((observer) => {
      if (!this.socket) {
        observer.error(new Error('Socket not connected'));
        observer.complete();
        return;
      }
      const handler = (data: T) => observer.next(data);
      this.socket.on(event, handler);
      return () => this.socket?.off(event, handler);
    });
  }

  joinRoom(room: string): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.socket) {
        observer.error(new Error('Socket not connected'));
        observer.complete();
        return;
      }

      if (this.rooms.has(room)) {
        observer.next();
        observer.complete();
        return;
      }

      this.socket.emit(SocketEvents.JOIN_ROOM, room, (ack: unknown) => {
        const ok =
          typeof ack === 'boolean'
            ? ack
            : typeof ack === 'string'
              ? ack.includes('Joined room')
              : false;
        if (ok) {
          this.rooms.add(room);
          observer.next();
          observer.complete();
        } else {
          observer.error(new Error(`Join room ack failed for ${room}`));
        }
      });
    });
  }

  leaveRoom(room: string): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.socket) {
        observer.error(new Error('Socket not connected'));
        observer.complete();
        return;
      }

      if (!this.rooms.has(room)) {
        observer.next();
        observer.complete();
        return;
      }

      this.socket.emit(SocketEvents.LEAVE_ROOM, room, () => {
        this.rooms.delete(room);
        observer.next();
        observer.complete();
      });
    });
  }

  private joinRoomInternal(room: string): void {
    if (!this.socket) return;
    this.socket.emit(SocketEvents.JOIN_ROOM, room, () => {
      // idempotent; ignore ack
    });
  }
}
