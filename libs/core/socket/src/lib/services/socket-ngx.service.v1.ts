import { CoreStateLibAuthTokensPageActions } from '@fincloud/core-state/auth-tokens';
import { selectUserCustomerKey } from '@fincloud/core-state/user';
import { TokenManagementService } from '@fincloud/core/auth';
import { MonitoringService } from '@fincloud/core/services';
import { AccessTokenStatus } from '@fincloud/types/enums';
import { concatLatestFrom } from '@ngrx/operators';
import { Store } from '@ngrx/store';
import {
  Observable,
  ReplaySubject,
  Subject,
  distinctUntilChanged,
  filter,
  map,
  shareReplay,
  switchMap,
  takeUntil,
  tap,
} from 'rxjs';
import { Socket, io } from 'socket.io-client';
import { SocketConnectionStatus } from '../enums/socket-connection-status';
import { SocketEvents } from '../enums/socket-events';
import { SocketTransports } from '../enums/socket-transports';
import { SocketType } from '../enums/socket-type';
import { SocketConfig } from '../models/socket-config';
import { SocketConnectionState } from '../models/socket-connection-state';
import { SocketIoOptions } from '../models/socket-options';
import { SocketQueryParams } from '../models/socket-query-params';
import { MANUAL_DISCONNECT_REASON } from '../utils/manual-disconnect-reason';
import { SOCKET_CONFIG_MAP } from '../utils/socket-config-map';
import { UNEXPECTED_DISCONNECT_REASONS } from '../utils/unexpected-disconnect-reasons';

/**
 * Version 1: Enhanced single-class implementation applying critical fixes while keeping the API.
 */
export class SocketNgxServiceEnhanced {
  private readonly rooms = new Map<string, { joined: boolean }>();

  private socket: Socket | null = null;
  private initSocketObservable$: Observable<SocketConnectionState> | null =
    null;
  private destroy$$ = new Subject<void>();
  private readonly connectionState$$ = new ReplaySubject<SocketConnectionState>(
    1,
  );
  private readonly socket$$ = new ReplaySubject<Socket>(1);

  constructor(
    private readonly socketType: SocketType,
    private readonly tokenManagementService: TokenManagementService,
    private readonly monitoringService: MonitoringService,
    private readonly store: Store,
  ) {}

  private get socketConfig(): SocketConfig {
    return SOCKET_CONFIG_MAP[this.socketType];
  }

  isConnected(): boolean {
    return !!this.socket && this.socket.connected === true;
  }

  initSocket(
    queryParams?: SocketQueryParams,
  ): Observable<SocketConnectionState> {
    if (this.initSocketObservable$) {
      return this.initSocketObservable$;
    }

    this.initSocketObservable$ = this.tokenManagementService.statusChange$.pipe(
      distinctUntilChanged(),
      concatLatestFrom(() => this.store.select(selectUserCustomerKey)),
      tap(([status]) => {
        if (status === AccessTokenStatus.INVALID) {
          this.invalidTokenManualDisconnect();
        }
      }),
      filter(([status]) => status === AccessTokenStatus.VALID),
      map(
        ([, customerKey]): SocketIoOptions =>
          this.socketOptions(customerKey, queryParams),
      ),
      switchMap(
        ({ uri, options }) =>
          new Observable<SocketConnectionState>((observer) => {
            // Already connected
            if (this.isConnected()) {
              const state: SocketConnectionState = {
                status: SocketConnectionStatus.CONNECTED,
                socket: this.socket,
              };
              this.connectionState$$.next(state);
              observer.next(state);
            } else {
              // Create new socket instance
              this.socket = io(uri, options);
              this.addSocketListeners();
              this.addManagerSocketListeners();

              // Re-join any remembered rooms (ack will mark as joined)
              if (this.rooms.size > 0) {
                Array.from(this.rooms.keys()).forEach((room) =>
                  this.socketJoinRoom(room),
                );
              }
            }

            // Bridge state updates to subscriber
            const sub = this.connectionState$$.subscribe(observer);
            return () => sub.unsubscribe();
          }),
      ),
      shareReplay({ refCount: true, bufferSize: 1 }),
      takeUntil(this.destroy$$),
    );

    return this.initSocketObservable$;
  }

  deactivateSocket(): Observable<void> {
    return new Observable<void>((observer) => {
      // idempotent
      this.destroy$$.next();
      this.initSocketObservable$ = null;

      try {
        this.socket?.removeAllListeners();
        this.socket?.disconnect();
      } catch (e) {
        this.monitoringService.logWebsocketsError(e as Error);
      }

      this.socket = null;
      this.rooms.clear();

      // recreate destroy subject for future init cycles
      this.destroy$$.complete();
      this.destroy$$ = new Subject<void>();

      observer.next();
      observer.complete();
    });
  }

  joinRoom(roomParam: string): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.isConnected()) {
        observer.error(
          new Error(
            `Socket: ${this.socketConfig.context} is not connected. Room: ${roomParam}`,
          ),
        );
        observer.complete();
        return;
      }

      // If already joined, finish immediately
      if (this.rooms.get(roomParam)?.joined) {
        observer.next();
        observer.complete();
        return;
      }

      this.socketJoinRoom(roomParam, observer);
    });
  }

  leaveRoom(room: string): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.isConnected()) {
        observer.error(
          new Error(`Socket: ${this.socketConfig.context} is not connected`),
        );
        observer.complete();
        return;
      }

      if (!this.rooms.has(room)) {
        observer.next();
        observer.complete();
        return;
      }

      this.socket.emit(SocketEvents.LEAVE_ROOM, room, () => {
        this.rooms.delete(room);
        observer.next();
        observer.complete();
      });
    });
  }

  sendMessage<T>(sendDestination: string, message: T): Observable<void> {
    return new Observable<void>((observer) => {
      if (!this.isConnected()) {
        observer.error(
          new Error(`Socket: ${this.socketConfig.context} is not connected`),
        );
        observer.complete();
        return;
      }

      try {
        this.socket.emit(sendDestination, message);
        observer.next();
      } catch (error) {
        this.monitoringService.logWebsocketsError(error as Error);
        observer.error(error);
      }

      observer.complete();
    });
  }

  // Connection-gated, auto-reattaches across reconnects/new sockets
  getMessagesByDestination<T>(receiveDestination: string): Observable<T> {
    return this.socket$$.pipe(
      switchMap(
        (socket) =>
          new Observable<T>((observer) => {
            const handler = (message: T) => observer.next(message);
            socket.on(receiveDestination, handler);
            return () => socket.off(receiveDestination, handler);
          }),
      ),
    );
  }

  joinRoomAndListen<T>(
    roomParam: string,
    receiveDestination: string,
  ): Observable<T> {
    return new Observable<T>((observer) => {
      const start = () => {
        const sub =
          this.getMessagesByDestination<T>(receiveDestination).subscribe(
            observer,
          );
        return () => sub.unsubscribe();
      };

      if (!this.isConnected()) {
        observer.error(
          new Error(
            `Socket: ${this.socketConfig.context} is not connected. Room: ${roomParam}`,
          ),
        );
        observer.complete();
        return;
      }

      // Ensure room joined then listen
      const sub = this.joinRoom(roomParam).subscribe({
        next: () => start(),
        error: (e) => observer.error(e),
      });

      return () => sub.unsubscribe();
    });
  }

  private socketJoinRoom(
    roomParam: string,
    observer?: { next: () => void; complete: () => void },
  ): void {
    if (this.rooms.get(roomParam)?.joined) {
      observer?.next?.();
      observer?.complete?.();
      return;
    }

    this.socket.emit(SocketEvents.JOIN_ROOM, roomParam, (ack: unknown) => {
      // Prefer a boolean/structured ack; keep backward compatibility with string
      const ok =
        typeof ack === 'boolean'
          ? ack
          : typeof ack === 'string'
            ? ack.includes('Joined room')
            : false;
      if (ok) {
        this.rooms.set(roomParam, { joined: true });
        observer?.next?.();
        observer?.complete?.();
      } else {
        this.monitoringService.logWebsocketsError(
          new Error(
            `Socket room join failed for ${roomParam} in ${this.socketConfig.namespace}`,
          ),
        );
      }
    });
  }

  private addSocketListeners(): void {
    if (!this.socket) return;

    this.socket.on(SocketEvents.CONNECT, () => {
      const state: SocketConnectionState = {
        status: SocketConnectionStatus.CONNECTED,
        socket: this.socket,
      };
      this.socket$$.next(this.socket);
      this.connectionState$$.next(state);
    });

    this.socket.on(SocketEvents.CONNECT_ERROR, (error: Error) => {
      this.monitoringService.logWebsocketsError(error);
      this.connectionState$$.next({
        status: SocketConnectionStatus.CONNECT_ERROR,
        error,
      });
    });

    this.socket.on(
      SocketEvents.DISCONNECT,
      (reason: string, details?: unknown) => {
        if (
          reason !== MANUAL_DISCONNECT_REASON &&
          UNEXPECTED_DISCONNECT_REASONS.includes(reason)
        ) {
          this.monitoringService.logWebsocketsError(
            new Error(
              `Unexpected disconnect from ${this.socketConfig.namespace}: ${reason}`,
            ),
          );
        }
        this.connectionState$$.next({
          status: SocketConnectionStatus.DISCONNECT,
          reason,
          details,
        });
      },
    );
  }

  private addManagerSocketListeners(): void {
    if (!this.socket) return;

    this.socket.io.on(SocketEvents.RECONNECT, () => {
      // Re-join rooms on manager-level reconnect
      Array.from(this.rooms.keys()).forEach((room) =>
        this.socketJoinRoom(room),
      );
      this.connectionState$$.next({
        status: SocketConnectionStatus.RECONNECT,
        socket: this.socket!,
      });
    });

    this.socket.io.on(SocketEvents.RECONNECT_ERROR, (error: Error) => {
      this.monitoringService.logWebsocketsError(error);
      this.connectionState$$.next({
        status: SocketConnectionStatus.RECONNECT_ERROR,
        error,
      });
    });

    this.socket.io.on(SocketEvents.RECONNECT_FAILED, () => {
      this.connectionState$$.next({
        status: SocketConnectionStatus.RECONNECT_FAILED,
      });
    });
  }

  private invalidTokenManualDisconnect(): void {
    if (!this.socket) return;

    this.store.dispatch(
      CoreStateLibAuthTokensPageActions.obtainRefreshTokenBySocket(),
    );
    Array.from(this.rooms.keys()).forEach((room) =>
      this.rooms.set(room, { joined: false }),
    );

    try {
      this.socket.disconnect();
    } catch (e) {
      this.monitoringService.logWebsocketsError(e as Error);
    }

    // Let consumers know we got disconnected due to token invalidation
    this.connectionState$$.next({
      status: SocketConnectionStatus.DISCONNECT,
      reason: MANUAL_DISCONNECT_REASON,
    });
  }

  private socketOptions(
    customerKey: string,
    queryParams?: SocketQueryParams,
  ): SocketIoOptions {
    const uri = `${this.socketConfig.socketUrl}${this.socketConfig.namespace}`;
    const token =
      this.tokenManagementService.getToken(customerKey)?.tokenRaw?.accessToken;

    if (!token) {
      this.monitoringService.logWebsocketsError(
        new Error(`Missing access token for ${this.socketConfig.namespace}`),
      );
    }

    const options = {
      path: `/${this.socketConfig.context}`,
      query: {
        ...(queryParams || {}),
        token: token ?? '',
      },
      transports: [SocketTransports.WEBSOCKET, SocketTransports.POLLING],
      autoConnect: true,
      reconnectionAttempts: 5,
    };

    return { uri, options };
  }
}
